<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邱大山，你真棒！- 3D中文特效</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            font-family: 'Courier New', monospace;
            color: #fff;
        }
        
        #container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }
        
        #info {
            position: absolute;
            top: 10px;
            left: 10px;
            z-index: 100;
            font-size: 13px;
            line-height: 1.4;
            background: rgba(0,0,0,0.7);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #00ffff;
        }
        
        #info a {
            color: #00ffff;
            text-decoration: none;
        }
        
        #info a:hover {
            text-decoration: underline;
        }
        
        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 20px;
            z-index: 200;
            text-align: center;
            background: rgba(0,0,0,0.8);
            padding: 30px;
            border-radius: 15px;
            border: 2px solid #00ffff;
        }
        
        .loading-bar {
            width: 250px;
            height: 6px;
            background: #333;
            margin: 20px auto;
            border-radius: 3px;
            overflow: hidden;
        }
        
        .loading-progress {
            height: 100%;
            background: linear-gradient(90deg, #00ffff, #ff00ff);
            width: 0%;
            animation: loading 2s ease-in-out infinite;
        }
        
        @keyframes loading {
            0% { width: 0%; }
            50% { width: 70%; }
            100% { width: 100%; }
        }
        
        #controls {
            position: absolute;
            bottom: 10px;
            left: 10px;
            z-index: 100;
            font-size: 12px;
            background: rgba(0,0,0,0.7);
            padding: 15px;
            border-radius: 8px;
            line-height: 1.4;
            border: 1px solid #00ffff;
        }
        
        .highlight {
            color: #00ffff;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div id="container">
        <div id="loading">
            <div>🎨 加载3D中文特效...</div>
            <div class="loading-bar">
                <div class="loading-progress"></div>
            </div>
            <div style="font-size: 14px; margin-top: 10px;">邱大山，你真棒！</div>
        </div>
        
        <div id="info">
            <strong class="highlight">3D中文文字特效</strong><br/>
            文字: <span class="highlight">"邱大山，你真棒！"</span><br/>
            <a href="https://threejs.org" target="_blank" rel="noopener">Three.js</a> ES模块版本
        </div>
        
        <div id="controls">
            <span class="highlight">控制说明:</span><br/>
            🖱️ 拖拽: 旋转视角<br/>
            🎯 滚轮: 缩放<br/>
            ⌨️ 空格: 自动旋转<br/>
            🔄 R键: 重置相机
        </div>
    </div>

    <!-- 使用ES模块方式加载Three.js -->
    <script type="importmap">
        {
            "imports": {
                "three": "https://unpkg.com/three@0.158.0/build/three.module.js",
                "three/addons/": "https://unpkg.com/three@0.158.0/examples/jsm/"
            }
        }
    </script>
    
    <script type="module">
        import * as THREE from 'three';
        import { OrbitControls } from 'three/addons/controls/OrbitControls.js';

        let camera, scene, renderer, controls;
        let group, textMeshes = [];

        const text = "邱大山，你真棒！";
        const textParams = {
            size: 80,
            height: 20,
            spacing: 140
        };

        let targetRotation = 0;

        init();
        animate();

        function init() {
            console.log('🚀 初始化3D中文特效...');
            
            // 场景 - 使用渐变背景色
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x1a1a2e);
            scene.fog = new THREE.Fog(0x1a1a2e, 500, 2000);

            // 相机
            camera = new THREE.PerspectiveCamera(45, window.innerWidth / window.innerHeight, 1, 2000);
            camera.position.set(0, 200, 800);

            // 渲染器
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setPixelRatio(window.devicePixelRatio);
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;
            renderer.toneMapping = THREE.ACESFilmicToneMapping;
            renderer.toneMappingExposure = 1.2;
            document.getElementById('container').appendChild(renderer.domElement);

            // 控制器
            controls = new OrbitControls(camera, renderer.domElement);
            controls.target.set(0, 0, 0);
            controls.enableDamping = true;
            controls.dampingFactor = 0.05;
            controls.autoRotate = true;
            controls.autoRotateSpeed = 1;

            // 光照
            setupLights();

            // 创建文字组
            group = new THREE.Group();
            scene.add(group);

            // 创建中文文字
            createChineseTextMeshes();

            // 事件监听
            setupEventListeners();

            // 隐藏加载界面
            setTimeout(() => {
                const loading = document.getElementById('loading');
                if (loading) loading.style.display = 'none';
                console.log('✅ 3D中文特效加载完成！');
            }, 1500);
        }

        function setupLights() {
            // 环境光 - 增强亮度
            const ambientLight = new THREE.AmbientLight(0x404040, 0.8);
            scene.add(ambientLight);

            // 主方向光 - 白色强光
            const directionalLight = new THREE.DirectionalLight(0xffffff, 1.5);
            directionalLight.position.set(100, 100, 50);
            directionalLight.castShadow = true;
            directionalLight.shadow.mapSize.width = 2048;
            directionalLight.shadow.mapSize.height = 2048;
            scene.add(directionalLight);

            // 彩色点光源1 - 青色
            const pointLight1 = new THREE.PointLight(0x00ffff, 2, 1000);
            pointLight1.position.set(-200, 100, 200);
            scene.add(pointLight1);

            // 彩色点光源2 - 洋红色
            const pointLight2 = new THREE.PointLight(0xff00ff, 2, 1000);
            pointLight2.position.set(200, 100, 200);
            scene.add(pointLight2);

            // 彩色点光源3 - 黄色
            const pointLight3 = new THREE.PointLight(0xffff00, 1.5, 1000);
            pointLight3.position.set(0, -100, 100);
            scene.add(pointLight3);
        }

        function createChineseTextMeshes() {
            console.log('🎨 创建中文3D文字...');
            const characters = ['邱', '大', '山', '你', '真', '棒', '！'];
            const startX = -(characters.length - 1) * textParams.spacing / 2;

            characters.forEach((char, index) => {
                const mesh = createChineseCharacterMesh(char, index);
                mesh.position.x = startX + index * textParams.spacing;
                mesh.position.y = 0;
                mesh.position.z = 0;
                
                group.add(mesh);
                textMeshes.push(mesh);
                
                console.log(`✨ 创建文字: ${char}`);
            });
        }

        function createChineseCharacterMesh(character, index) {
            // 创建立方体几何体 - 更大更明显
            const geometry = new THREE.BoxGeometry(
                textParams.size, 
                textParams.size, 
                textParams.height
            );

            // 创建高对比度文字纹理
            const canvas = document.createElement('canvas');
            const context = canvas.getContext('2d');
            canvas.width = 512;
            canvas.height = 512;

            // 深色背景
            context.fillStyle = '#000000';
            context.fillRect(0, 0, canvas.width, canvas.height);

            // 绘制白色文字
            context.fillStyle = '#ffffff';
            context.font = 'bold 300px "Microsoft YaHei", "SimHei", "黑体", Arial, sans-serif';
            context.textAlign = 'center';
            context.textBaseline = 'middle';
            
            // 添加发光效果
            context.shadowColor = '#00ffff';
            context.shadowBlur = 30;
            context.fillText(character, canvas.width/2, canvas.height/2);
            
            // 再次绘制以增强效果
            context.shadowBlur = 0;
            context.fillStyle = '#ffffff';
            context.fillText(character, canvas.width/2, canvas.height/2);

            const texture = new THREE.CanvasTexture(canvas);
            texture.generateMipmaps = false;
            texture.minFilter = THREE.LinearFilter;

            // 创建发光材质
            const material = new THREE.MeshPhongMaterial({ 
                map: texture,
                color: 0xffffff,
                emissive: 0x222222,
                shininess: 100,
                transparent: false
            });

            const mesh = new THREE.Mesh(geometry, material);
            mesh.castShadow = true;
            mesh.receiveShadow = true;

            return mesh;
        }

        function setupEventListeners() {
            // 键盘事件
            document.addEventListener('keydown', onDocumentKeyDown, false);
            
            // 窗口大小调整
            window.addEventListener('resize', onWindowResize, false);
        }

        function onDocumentKeyDown(event) {
            switch(event.code) {
                case 'Space':
                    event.preventDefault();
                    controls.autoRotate = !controls.autoRotate;
                    console.log('🔄 自动旋转:', controls.autoRotate ? '开启' : '关闭');
                    break;
                case 'KeyR':
                    camera.position.set(0, 200, 800);
                    controls.target.set(0, 0, 0);
                    controls.update();
                    console.log('🎯 相机重置');
                    break;
            }
        }

        function onWindowResize() {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        }

        function animate() {
            requestAnimationFrame(animate);

            const time = Date.now() * 0.001;

            // 文字动画
            textMeshes.forEach((mesh, index) => {
                const offset = index * 0.5;
                
                // 浮动效果
                mesh.position.y = Math.sin(time * 1.5 + offset) * 30;
                
                // 独立旋转
                mesh.rotation.x = Math.sin(time * 1.2 + offset) * 0.2;
                mesh.rotation.z = Math.cos(time * 0.8 + offset) * 0.1;
                
                // 彩虹色彩变化
                const hue = (time * 0.3 + index * 0.15) % 1;
                mesh.material.color.setHSL(hue, 0.8, 0.9);
                
                // 发光效果变化
                const emissiveIntensity = 0.1 + Math.sin(time * 2 + offset) * 0.1;
                mesh.material.emissive.setHSL(hue, 0.5, emissiveIntensity);
            });

            // 更新控制器
            controls.update();

            // 渲染
            renderer.render(scene, camera);
        }
    </script>
</body>
</html>
