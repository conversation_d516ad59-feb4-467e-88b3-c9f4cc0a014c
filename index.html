<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邱大山，你真棒！- 3D文字效果</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background: linear-gradient(45deg, #000428, #004e92);
            font-family: 'Arial', sans-serif;
        }
        
        #container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }
        
        #info {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            z-index: 100;
            font-size: 14px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
        }
        
        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 24px;
            z-index: 200;
            text-align: center;
        }
        
        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
            margin: 20px auto;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        #controls {
            position: absolute;
            bottom: 20px;
            left: 20px;
            color: white;
            z-index: 100;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div id="container">
        <div id="loading">
            <div class="loading-spinner"></div>
            <div>加载中...</div>
        </div>
        
        <div id="info">
            <h2>🎉 邱大山，你真棒！🎉</h2>
            <p>Three.js 3D文字特效展示</p>
        </div>
        
        <div id="controls">
            <p>🖱️ 鼠标移动：控制视角</p>
            <p>🎨 自动变换：彩虹色彩</p>
            <p>✨ 粒子效果：动态背景</p>
        </div>
    </div>

    <!-- Three.js CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/loaders/FontLoader.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/geometries/TextGeometry.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.js"></script>
    
    <script src="main.js"></script>
</body>
</html>
